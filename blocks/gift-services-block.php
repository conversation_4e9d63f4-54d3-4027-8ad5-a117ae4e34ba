<?php
$loop = new WP_Query(array(
  'post_type' => 'service',
  'posts_per_page' => -1,
  'orderby' => 'menu_order title',
  'order' => 'DESC'
));
?>
<section class="giftServicesBlock paddingTop <?= esc_attr(gift631_get_block_classes()) ?>" data-init>
  <div class="contentWrapper">
    <div class="cols">
      <div class="col">
        <div class="sectionHeader" data-sticky-top>
          <h2 class="bigTitle" data-lines data-words><?php the_field('title') ?></h2>
          <div class="text"><p><?php the_field('text') ?></p></div>
          <?php if(get_field('button')): render_button_from_array(get_field('button'), "outline"); endif; ?>
        </div>
      </div>
      <div class="col">
        <?php while($loop->have_posts()): $loop->the_post(); $icon = get_field('service_icon', get_the_ID()); ?>
          <div class="service" data-init>
            <div class="innerCol">
              <h3 class="normalTitle"><?= esc_html(get_the_title()) ?></h3>
              <div class="text"><?php the_field('service_description', get_the_ID()); ?></div>
            </div>
            <div class="innerCol imageCol">
                <?php if ($icon): $img = optimize_images_for_compressx($icon); ?>
                  <img class="lazy" data-src="<?= esc_url($img['sizes']['medium_large'] ?? $img['url']) ?>" alt="<?= esc_attr(get_the_title()) ?>">
                <?php endif; ?>
            </div>
          </div>
        <?php endwhile; wp_reset_postdata(); ?>
      </div>
    </div>
  </div>
</section>

