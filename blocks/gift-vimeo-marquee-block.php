<?php
// Template: media marquee met repeater 'videos' (sub field 'url')
// Vereisten / Notities:
// - ffmpeg moet op de server beschikbaar zijn voor de fallback.
// - exec() moet beschikbaar zijn als je de ffmpeg-fallback wil gebruiken.
// - <PERSON> gege<PERSON>de thumbs worden opgeslagen in wp-content/uploads/vimeo-thumbs/
// - Caching: thumbs worden als transient opgeslagen om werk te besparen.

if ( have_rows('videos') ): 
    $default_thumb = get_template_directory_uri() . '/assets/images/video-placeholder.jpg';
?>
<section class="mediaMarqueeBlock <?php the_field('background') ?>" data-init <?php if (get_field('anchor')): ?>data-anchor="<?php the_field('anchor') ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <h2 class="mediumTitle centered primary" data-lines data-words><?php the_field('title') ?></h2>
    </div>
    <div class="marqueeWrapper" data-init>
        <div class="marquee" data-marquee data-marquee-direction="right" data-marquee-speed="25" data-marquee-scroll-speed="5" data-marquee-swipe="true">
            <div class="marqueeScroll">
                <div class="itemsContainer">

                <?php while ( have_rows('videos') ) : the_row();
                    $video_url = trim( get_sub_field('url') );
                    if (empty($video_url)) continue;

                    // cache key
                    $cache_key = 'vimeo_thumb_' . md5($video_url);
                    $thumb = get_transient($cache_key);
                    if ( ! $thumb ) {
                        $thumb = $default_thumb; // default fallback

                        // 1) Try oEmbed on the provided URL
                        $oembed_url = 'https://vimeo.com/api/oembed.json?url=' . rawurlencode($video_url);
                        $resp = wp_remote_get( $oembed_url, array( 'timeout' => 8 ) );

                        if ( ! is_wp_error($resp) && wp_remote_retrieve_response_code($resp) === 200 ) {
                            $data = json_decode( wp_remote_retrieve_body($resp) );
                            if ( ! empty( $data->thumbnail_url ) ) {
                                $thumb = esc_url( $data->thumbnail_url );
                            }
                        }

                        // 2) If still fallback: try to extract a normal Vimeo video id and call api/v2
                        if ( $thumb === $default_thumb ) {
                            // matches e.g. https://vimeo.com/123456789 or https://player.vimeo.com/video/123456789
                            if ( preg_match('/vimeo\.com\/(?:.*\/)?([0-9]{6,})/', $video_url, $m) || preg_match('/player\.vimeo\.com\/video\/([0-9]{6,})/', $video_url, $m) ) {
                                $video_id = $m[1];
                                $v2 = wp_remote_get("https://vimeo.com/api/v2/video/{$video_id}.json", array('timeout' => 8));
                                if ( ! is_wp_error($v2) && wp_remote_retrieve_response_code($v2) === 200 ) {
                                    $d = json_decode( wp_remote_retrieve_body($v2) );
                                    if ( ! empty($d[0]->thumbnail_large) ) {
                                        $thumb = esc_url( $d[0]->thumbnail_large );
                                    }
                                }
                            }
                        }

                        // 3) If still fallback and it's a progressive URL: try HEAD to see redirect target and call oEmbed on that
                        if ( $thumb === $default_thumb && preg_match('/playback\/(\d+)\//', $video_url) ) {
                            $head = wp_remote_head( $video_url, array('timeout'=>8, 'redirection'=>5) );
                            if ( ! is_wp_error($head) ) {
                                $location = wp_remote_retrieve_header( $head, 'location' );
                                if ( $location ) {
                                    // try oEmbed on redirected location
                                    $resp2 = wp_remote_get( 'https://vimeo.com/api/oembed.json?url=' . rawurlencode($location), array('timeout'=>8) );
                                    if ( ! is_wp_error($resp2) && wp_remote_retrieve_response_code($resp2) === 200 ) {
                                        $dat2 = json_decode( wp_remote_retrieve_body($resp2) );
                                        if ( ! empty($dat2->thumbnail_url) ) {
                                            $thumb = esc_url( $dat2->thumbnail_url );
                                        }
                                    }
                                }
                            }
                        }

                        // 4) Last resort: try to create a thumbnail with ffmpeg (heavy; use caching)
                        if ( $thumb === $default_thumb && function_exists('exec') ) {
                            $uploads = wp_upload_dir();
                            $dir = trailingslashit( $uploads['basedir'] ) . 'vimeo-thumbs/';
                            if ( ! file_exists( $dir ) ) {
                                wp_mkdir_p( $dir );
                            }
                            $jpg_name = md5($video_url) . '.jpg';
                            $jpg_path = $dir . $jpg_name;
                            $jpg_url  = trailingslashit( $uploads['baseurl'] ) . 'vimeo-thumbs/' . $jpg_name;

                            // Only generate if file not exists
                            if ( ! file_exists($jpg_path) ) {
                                // Escape shell args
                                $in  = escapeshellarg( $video_url );
                                $out = escapeshellarg( $jpg_path );
                                // Seek to 0 or 1 second to increase chance of valid frame
                                $cmd = "ffmpeg -y -hide_banner -loglevel error -ss 00:00:01 -i {$in} -frames:v 1 -q:v 2 {$out} 2>&1";
                                @exec( $cmd, $output, $ret );
                                if ( $ret === 0 && file_exists($jpg_path) ) {
                                    $thumb = esc_url( $jpg_url );
                                }
                            } else {
                                $thumb = esc_url( $jpg_url );
                            }
                        }

                        // store in transient for 24 uur (of fallback indien default)
                        set_transient( $cache_key, $thumb, DAY_IN_SECONDS );
                    } // end if no cached thumb
                ?>
                    <div class="item" data-video data-video-url="<?php echo esc_url( $video_url ); ?>">
                        <?php if ( ! empty( $video_url ) ): ?>
                            <video 
                                class="lazy" 
                                playsinline 
                                autoplay 
                                muted 
                                loop
                                preload="none"
                                poster="<?php echo esc_url( $thumb ); ?>"
                                data-src="<?php echo esc_url( $video_url ); ?>">
                                <source src="<?php echo esc_url( $video_url ); ?>" type="video/mp4">
                            </video>
                        <?php else: ?>
                            <div class="placeholder"></div>
                        <?php endif; ?>

                        <div class="playIcon">
                            <svg width="68" height="48" viewBox="0 0 68 48">
                                <path d="M 45,24 27,14 27,34" fill="#fff"></path>
                            </svg>
                        </div>
                    </div>
                <?php endwhile; // have_rows ?>

                </div>
            </div>
        </div>
    </div>
</section>
<div class="videoOverlay" style="display: none;">
    <div class="innerOverlay">
        <div class="closeButton">
            <svg viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41Z"/>
            </svg>
        </div>
    </div>
</div>

<?php endif; // have_rows ?>
