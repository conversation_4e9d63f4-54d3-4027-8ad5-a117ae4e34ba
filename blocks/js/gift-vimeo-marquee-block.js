$(document).ready(function() {
    $(document).on("initPage", function() {
        if ($(".mediaMarqueeBlock").length > 0) {
            initMediaMarqueeBlock();
        }
    });
});

function initMediaMarqueeBlock() {
    $(".mediaMarqueeBlock").each(function(i, el){
        var $block = $(el);
        var $marquee = $block.find(".marquee");
        var $items = $marquee.find(".item");
        var $overlay = $(".videoOverlay");
        var $closeButton = $overlay.find(".closeButton");
        var $innerOverlay = $overlay.find(".placeVideo");
        var $videos = $items.find("video");

        // Store reference to marquee timeline for pause/play functionality
        var marqueeTimeline = null;

        // Find the marquee timeline from GSAP
        function getMarqueeTimeline() {
            if (!marqueeTimeline) {
                // Look for existing GSAP timeline on the marquee element
                var marqueeElement = $marquee[0];
                if (marqueeElement && marqueeElement._gsap) {
                    // Find timeline in GSAP's registry
                    gsap.globalTimeline.getChildren(true, true, false).forEach(function(tl) {
                        if (tl.targets && tl.targets().includes($marquee.find('.itemsContainer')[0])) {
                            marqueeTimeline = tl;
                        }
                    });
                }
            }
            return marqueeTimeline;
        }

        // Function to extract Vimeo video ID from URL
        function getVimeoVideoId(url) {
            if (!url) return null;

            // Handle various Vimeo URL formats
            var patterns = [
                /vimeo\.com\/(\d+)/,
                /vimeo\.com\/video\/(\d+)/,
                /player\.vimeo\.com\/video\/(\d+)/,
                /vimeo\.com\/(?:.*\/)?(\d{6,})/,  // Generic pattern for any Vimeo URL with ID
                /playback\/(\d+)\//  // Progressive redirect URLs like playback/1116689393/
            ];

            for (var i = 0; i < patterns.length; i++) {
                var match = url.match(patterns[i]);
                if (match && match[1]) {
                    return match[1];
                }
            }

            return null;
        }

        $(document).off("click", ".mediaMarqueeBlock .item").on("click", ".mediaMarqueeBlock .item", function() {
            var $item = $(this);
            var videoUrl = $item.attr("data-video-url");
            var vimeoId = getVimeoVideoId(videoUrl);

            console.log("Video URL:", videoUrl);
            console.log("Extracted Vimeo ID:", vimeoId);

            if (!vimeoId) {
                console.error("Could not extract Vimeo ID from URL:", videoUrl);
                return;
            }

            // Pause marquee animation
            var timeline = getMarqueeTimeline();
            if (timeline) {
                timeline.pause();
            }

            // Create placeVideo div with Vimeo iframe
            var vimeoIframe =
                '<iframe src="https://player.vimeo.com/video/' + vimeoId + '?autoplay=1&loop=0&byline=0&title=0&portrait=0" ' +
                'width="100%" height="100%" frameborder="0" ' +
                'allow="autoplay; fullscreen; picture-in-picture" allowfullscreen>' +
                '</iframe>';

            $innerOverlay.html(vimeoIframe);
            $overlay.show();
        });

        $closeButton.on("click", function() {
            // Resume marquee animation
            var timeline = getMarqueeTimeline();
            if (timeline) {
                timeline.play();
            }

            $overlay.hide();
        });

        // Also handle ESC key to close overlay
        $(document).on("keydown", function(e) {
            if (e.keyCode === 27 && $overlay.is(":visible")) { // ESC key
                $closeButton.trigger("click");
            }
        });
    });
}
