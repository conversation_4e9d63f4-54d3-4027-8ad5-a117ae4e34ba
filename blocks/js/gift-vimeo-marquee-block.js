$(document).ready(function() {
    $(document).on("initPage", function() {
        if ($(".mediaMarqueeBlock").length > 0) {
            initMediaMarqueeBlock();
        }
    });
});

function initMediaMarqueeBlock() { 
    $(".mediaMarqueeBlock").each(function(i, el){
        var $block = $(el);
        var $marquee = $block.find(".marquee");
        var $items = $marquee.find(".item");
        var $overlay = $block.find(".videoOverlay");
        var $closeButton = $overlay.find(".closeButton");
        var $innerOverlay = $overlay.find(".innerOverlay");
        var $videos = $items.find("video");

        $(document).off("click", ".mediaMarqueeBlock .item").on("click", ".mediaMarqueeBlock .item", function(e) {
            console.log("click");
            var $item = $(this);
            var $video = $item.find("video");
            var $src = $video.attr("data-src");
            var $poster = $video.attr("poster");
            console.log(
                "src: " + $src,
                "poster: " + $poster,
                "item: " + $item[0],
                "video: " + $video,
                "innerOverlay: " + $innerOverlay,
                "overlay: " + $overlay,
                "closeButton: " + $closeButton
            )
            $innerOverlay.html('<video src="' + $src + '" poster="' + $poster + '" autoplay muted loop playsinline></video>');
            $overlay.fadeIn();
        });

        $closeButton.on("click", function() {
            $overlay.fadeOut();
        });
    });
}
