<?php
$image = get_field('book_image');
$title = get_field('title');
$subtitle = get_field('subtitle');
$purchase_links = get_field('purchase_links'); // repeater field

// Default links if none are set
if (empty($purchase_links)) {
    $purchase_links = array(
        array('region' => 'Asia', 'url' => 'https://amzn.asia/d/d0bP52M'),
        array('region' => 'Europe', 'url' => 'https://amzn.eu/d/72aMnls'),
        array('region' => 'US', 'url' => 'https://a.co/d/dnaleMn'),
        array('region' => 'US (Alt)', 'url' => 'https://a.co/d/e7ls0fW'),
        array('region' => 'India', 'url' => 'https://amzn.in/d/0er5wIt'),
    );
}
?>
<section class="giftBookPurchaseBlock dark" data-init data-show-cursor>
  <div class="contentWrapper">
    <div class="bookPurchase">
      <?php if($image): ?>
        <div class="bookImage">
          <?php 
          $i = optimize_images_for_compressx($image); 
          ?>
          <img class="lazy" data-src="<?= esc_url($i['sizes']['large'] ?? $i['url']) ?>" alt="<?= esc_attr($image['alt'] ?? '') ?>">
        </div>
      <?php endif; ?>
      
      <div class="bookInfo">
        <?php if($title): ?>
          <h2 class="normalTitle" data-lines data-words><?= esc_html($title) ?></h2>
        <?php endif; ?>
        
        <?php if($subtitle): ?>
          <p class="bookSubtitle"><?= esc_html($subtitle) ?></p>
        <?php endif; ?>
        
        <?php if($purchase_links): ?>
          <div class="purchaseLinks">
            <?php foreach($purchase_links as $link): 
              $region = $link['region'];
              $url = $link['url'];
              if($region && $url): ?>
                <?php render_button_from_array(array('url' => $url, 'title' => 'Buy on Amazon in ' . $region), 'primary smaller', 'target="_blank"'); ?>
              <?php endif; ?>
            <?php endforeach; ?>
          </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
</section>
