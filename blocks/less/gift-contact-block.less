// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftContactBlock {
    position: relative;
    .cols {
        display: flex;
        gap: @vw16;
    }
    .col {
        width: 100%;
        display: inline-flex;
        flex-direction: column;
        justify-content: start;
        align-items: start;
        position: relative;
    }
    form {
        width: 100%;
        .mediumTitle {
            display: none;
        }
        .wpcf7-form-control-wrap {
            width: 100%;
        }
         textarea {
            .rounded(@vw16) !important;
        }
        .buttonWrapper {
            text-align: left;
        }
    }
}

@media all and (max-width: 1080px) {
    .giftContactBlock {
        .cols {
            gap: @vw16-1080;
        }
        .col {
            .sectionHeader {
                margin-bottom: @vw50-1080;
            }
        }
    }
}

@media all and (max-width: 580px) {
  .giftContactBlock {
        .cols {
            gap: @vw16-580;
            flex-direction: column;
        }
        .col {
            .sectionHeader {
                margin-bottom: @vw50-580;
            }
        }
    }
}