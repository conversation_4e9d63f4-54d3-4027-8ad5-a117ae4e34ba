// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftStickyBigMedia {
    .contentWrapper {
        height: 100%;
        position: relative;
    }
    .mediaWrapper {
        position: relative;
        margin: auto;
        display: block;
        overflow: hidden;
        .rounded(@vw14);
        width: calc(100% ~"-" @vw100 * 4.04); 
        height: auto;
        .innerImage {
            position: relative;
            top: 0;
            left: 0;
            width: 100%;
            .paddingRatio(16,10);
            height: 0;
            .item {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                opacity: 0;
                .transitionMore(opacity, .3s);
                transition-delay: .15s;
                &.active {
                    opacity: 1;
                    transition-delay: 0s;
                }
            }
            img, video {
                position: absolute;
                top: 50%;
                left: 50%;
                .transform(translate(-50%, -50%));
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
    }

    // Opposite/inverted animation styles
    &.opposite {
        .mediaWrapper {
            width: 100%; // Start at full width for opposite animation
        }
    }
}

@media all and (max-width: 1080px) {
    .giftStickyBigMedia {
        .mediaWrapper {
            .rounded(@vw14-1080);
            width: calc(100% ~"-" @vw100-1080 * 2);
        }
    }
}

@media all and (max-width: 580px) {
    .giftStickyBigMedia {
        .mediaWrapper {
            .rounded(@vw14-580);
            width: calc(100% ~"-" @vw100-580 * 2);
            .innerImage {
                .paddingRatio(10,16);
            }
        }
    }
}