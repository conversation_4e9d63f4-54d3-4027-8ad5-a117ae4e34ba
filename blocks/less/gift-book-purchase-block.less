// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftBookPurchaseBlock {
  .bookPurchase {
    display: flex;
    align-items: center;
    gap: @vw32;
    margin: 0 auto;
  }
  .bookImage {
    flex-shrink: 0;
    width: 33.3333%;
    img {
      height: auto;
      width: 100%;
      max-width: 100% !important;
    }
  }
  .purchaseLinks {
    display: flex;
    flex-wrap: wrap;
    gap: @vw16;
    margin-top: @vw50;
  }
  .button {
    .word {
      transition-delay: 0s !important;
    }
  }
}

@media all and (max-width: 1080px) {
  .giftBookPurchaseBlock {
    .bookPurchase {
      gap: @vw32-1080;
    }
    .bookImage {
      width: 50%;
    }
    .bookInfo {
      width: 50%;
    }
    .purchaseLinks {
      gap: @vw16-1080;
      margin-top: @vw50-1080;
    }

    
  }
}

@media all and (max-width: 580px) {
  .giftBookPurchaseBlock {
    .bookPurchase {
      flex-direction: column;
      text-align: center;
      gap: @vw30-580;
    }
    .bookImage {
      width: 100%;
    }
    .bookInfo {
      width: 100%;
    }
    .purchaseLinks {
      gap: @vw16-580;
      margin-top: @vw50-580;
    }
    .button {
      width: 100%;
    }
  }
}