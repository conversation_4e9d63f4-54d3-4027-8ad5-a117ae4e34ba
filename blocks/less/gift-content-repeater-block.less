// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.giftContentRepeater {
    &:not(.contentItem:nth-child(2)) {
        .contentItems {
            margin: auto;
            max-width: 80%;
            display: block;
            .contentItem {
                width: 100%;
                display: inline-flex;
                flex-direction: row;
                .innerCol {
                    width: 50%;
                }
            }
        }
    }
    &.inview {
        .contentItem {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            -webkit-transition: opacity .45s, transform .45s;
            .stagger(6, 0.15s, 0.9s);
        }
    }
    .bigTitle, .hugeTitle {
        text-align: center;
        margin-bottom: @vw100;
    }
    
    .contentItems {
        display: flex;
        flex-direction: row;
        gap: @vw16;
    }
    
    .contentItem {
        padding: @vw50 @vw30;
        border: 1px solid @primaryColor;
        opacity: 0;
        .transform(translateY(@vw16));
        .rounded(@vw14);
        .normalTitle {
            margin-bottom: @vw30;
        }
        .text {
            margin-bottom: @vw50;
            p {
                margin-bottom: @vw20;
                &:last-child {
                    margin-bottom: 0;
                }
                strong {
                    text-transform: uppercase;
                    letter-spacing: .14em;
                    font-weight: 300;
                    color: rgba(@secondaryColor, .5);
                }
            }
        }
        
        .button {
            display: inline-block;
        }
    }
}

@media all and (max-width: 1080px) {
    .giftContentRepeater {
        .bigTitle, .hugeTitle {
            margin-bottom: @vw100-1080;
        }
        .contentItems {
            gap: @vw16-1080;
            flex-wrap: wrap;
            justify-content: center;
        }
        .contentItem {
            padding: @vw40-1080 @vw30-1080;
            .transform(translateY(@vw16-1080));
            .rounded(@vw14-1080);
            width: calc(50% - @vw8-1080);
            .normalTitle {
                margin-bottom: @vw30-1080;
            }
            .text {
                margin-bottom: @vw40-1080;
                p {
                    margin-bottom: @vw20-1080;
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .giftContentRepeater {
        &:not(.contentItem:nth-child(2)) {
            .contentItems {
                max-width: 100%;
                .contentItem {
                    flex-direction: column;
                    gap: @vw16-580;
                    .innerCol {
                        width: 100%;
                    }
                }
            }
        }
        .bigTitle, .hugeTitle {
            margin-bottom: @vw100-580;
        }
        .contentItems {
            gap: @vw16-580;
            flex-direction: column;
        }
        .contentItem {
            padding: @vw40-580 @vw30-580;
            .transform(translateY(@vw16-580));
            .rounded(@vw14-580);
            width: 100%;
            .normalTitle {
                margin-bottom: @vw30-580;
            }
            .text {
                margin-bottom: @vw40-580;
                p {
                    margin-bottom: @vw20-580;
                }
            }
        }
    }
}
