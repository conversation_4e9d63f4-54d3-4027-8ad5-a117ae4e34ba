// out: false
.mediaMarqueeBlock {
    .contentWrapper {
        margin-bottom: @vw80;
        text-align: center;
    }
    .marquee {
        white-space: nowrap;
        .itemsContainer {
            display: inline-block;
            .item {
                display: inline-block;
                position: relative;
                overflow: hidden;
                .rounded(@vw20);
                width: @vw100 * 8;
                height: @vw100 * 4.5;
                margin: 0 @vw8;
                cursor: pointer;
                * {
                    cursor: pointer;
                }
                &:hover {
                    .playIcon {
                        background: @secondaryColor;
                    }
                }
                .placeholder, video {
                    width: 100%;
                    height: 100%;
                    background: @hardWhite;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
                video {
                  object-fit: cover;
                  .transform(scale(1.4));
                }
                .playIcon {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    .transform(translate(-50%, -50%));
                    width: @vw100;
                    height: @vw100;
                    .rounded(50%);
                    pointer-events: none;
                    background: @primaryColor;
                    .transitionMore(background, .3s);
                    svg {
                      position: absolute;
                      top: 50%;
                      left: 50%;
                      .transform(translate(-50%, -50%));
                      width: 50%;
                      height: auto;
                      object-fit: contain;
                      path {
                        fill: @almostWhite;
                      }
                    }
                }
                img {
                    position: absolute;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    pointer-events: none;
                    object-fit: cover;
                }
            }
        }
    }
}

.videoOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(@secondaryColor, 0.8);
  }
  .innerOverlay {
    position: absolute;
    width: @vw100 * 10;
    height: @vw100 * 5.625;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 50%;
    top: 50%;
    .transform(translate(-50%, -50%));
    background: @primaryColor;
    .placeVideo {
      width: 100%;
      height: 100%;
      position: relative;
      iframe {
        width: 100%;
        height: 100%;
        border: none;
      }
    }
    .closeButton {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
      .transform(translate(150%, -150%));
      .transitionMore(transform, .3s);
      cursor: pointer;
      z-index: 10;
      * {
        cursor: pointer;
      }
      &:hover {
        .transform(translate(150%, -150%) rotate(90deg));
      }
      svg {
        width: @vw30;
        height: auto;
        path {
          fill: @hardWhite;
        }
      }
    }
  }
}

@media (max-width: 1080px) {
    .mediaMarqueeBlock {
      .contentWrapper {
        margin-bottom: @vw80-1080;
      }
      .marquee {
        .itemsContainer {
          .item {
            width: (@vw112-1080 * 4) + (@vw16-1080 * 2);
            margin: 0 @vw6-1080;
            .playIcon {
              width: @vw100-1080;
              height: @vw100-1080;
            }
          }
        }
      }
    }
    .videoOverlay {
      .innerOverlay {
        width: 90vw;
        height: 50.625vw; // 16:9 aspect ratio
        max-height: 80vh;
        .closeButton {
          svg {
            width: @vw30-1080;
          }
        }
      }
    }
  }
  
  @media (max-width: 580px) {
    .mediaMarqueeBlock {
      .contentWrapper {
        margin-bottom: @vw40-580;
      }
      .marquee {
        .itemsContainer {
          .item {
            width: (@vw112-580 * 4);
            margin: 0 @vw6-580;
            .playIcon {
              width: @vw70-580;
              height: @vw70-580;
              svg {
                width: 70%;
              }
            }
          }
        }
      }
    }
    .videoOverlay {
      .innerOverlay {
        width: 95vw;
        height: 53.4375vw; // 16:9 aspect ratio
        max-height: 85vh;
        .closeButton {
          svg {
            width: @vw30-580;
          }
        }
      }
    }
  }
  