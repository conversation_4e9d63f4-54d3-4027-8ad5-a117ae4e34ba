// out: false
@import '../vw_values.less';
@import '../constants.less';
.select2-container--default.select2-container--open.select2-container--below .select2-selection--single, .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
    border-bottom-left-radius: inherit;
    border-bottom-right-radius: inherit;
}
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
    background: rgba(@secondaryColor, .6) !important;
}
.wpcf7-form {
    margin: 0 auto;
    text-align: left;
    width: 70%;
    p {
        margin-bottom: 0;
    }

    label {
        display: none;
    }

    // Input velden
    input.wpcf7-form-control,
    textarea.wpcf7-form-control,
    select.wpcf7-form-control {
        width: 100%;
        padding: 0.8rem 1rem;
        background: transparent;
        border-color: rgba(@hardWhite, .1);
        .rounded(@vw60);
        color: @primaryColor;
        font-size: @vw16;
        font-family: 'Figtree', sans-serif;
        outline: none;
        letter-spacing: .14em;
        text-transform: uppercase;
        .transition(.3s);
        &::placeholder {
            color: #cccccc;
            opacity: 0.7;
        }
        &:focus {
            border-color: rgba(@primaryColor, .4);
        }
    }

    .wpcf7-form-control-wrap {
        display: inline-block;
        vertical-align: top;
        width: 50%;
        &:not(:last-child) {
            margin-bottom: @vw10;
        }
        &:has([name="email-address"]) {
            width: 100%;
        }
    }

    .wpcf7-not-valid-tip, .wpcf7-response-output {
        margin-top: @vw5;
        color: rgba(@hardWhite, .4);
        background: rgba(255,0,0,.1);
        border: 1px solid rgba(255,0,0,.2) !important;
        .rounded(@vw10);
        padding: @vw10 @vw16 !important;
        font-size: @vw16;
    }

    textarea {
        min-height: @vw100;
        height: @vw100 * 2;
        resize: none;
    }
    .buttonWrapper {
        margin-top: @vw40;
        margin-bottom: 0;
        text-align: center;
    }
    // Acceptance wrap full width
    .wpcf7-form-control-wrap[data-name^="acceptance"] {
        width: 100%;
        display: block;
        margin-top: @vw20;
    }

    // Show label for acceptance (override your label { display:none })
    .wpcf7-acceptance .wpcf7-list-item > label {
        display: inline-flex !important;
        align-items: center;
        gap: @vw12;
        cursor: pointer;
    }

    // Select2 custom styling to match theme
    .select2-container {
        width: 100% !important;

        .select2-selection--single {
            height: auto !important;
            padding: 0.8rem 1rem;
            background: transparent !important;
            border: 1px solid rgba(@hardWhite, .1) !important;
            .rounded(@vw60);
            color: @primaryColor;
            font-size: @vw16;
            font-family: 'Figtree', sans-serif;
            letter-spacing: .14em;
            text-transform: uppercase;
            .transition(.3s);

            .select2-selection__rendered {
                color: @primaryColor;
                line-height: 1.2;
                padding: 0;
                letter-spacing: .14em;
                text-transform: uppercase;
            }

            .select2-selection__arrow {
                height: 100%;
                right: 1rem;

                b {
                    border-color: @primaryColor transparent transparent transparent;
                    margin-top: -2px;
                }
            }

            &:focus,
            &:hover {
                border-color: rgba(@primaryColor, .4) !important;
            }
        }
    }

    .select2-container--open .select2-selection--single {
        border-color: rgba(@primaryColor, .4) !important;

        .select2-selection__arrow b {
            border-color: transparent transparent @primaryColor transparent;
        }
    }

    .select2-dropdown {
        background: rgba(@almostBlack, 0.95) !important;
        border: 1px solid rgba(@primaryColor, .4) !important;
        .rounded(@vw10);
        backdrop-filter: blur(10px);

        .select2-results__options {
            .select2-results__option {
                color: @hardWhite;
                font-family: 'Figtree', sans-serif;
                font-size: @vw16;
                letter-spacing: .14em;
                text-transform: uppercase;
                padding: 0.8rem 1rem;
                .transition(.3s);

                &:hover,
                &.select2-results__option--highlighted {
                    background: rgba(@primaryColor, .2) !important;
                    color: @primaryColor !important;
                }

                &.select2-results__option--selected {
                    background: rgba(@primaryColor, .4) !important;
                    color: @hardWhite !important;
                }
            }
        }
    }

    // Hide the native checkbox
    .wpcf7-acceptance input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
        pointer-events: none;
    }

    // Text label styling
    .wpcf7-acceptance .wpcf7-list-item-label {
        display: inline-flex;
        align-items: center;
        font-family: 'Figtree', sans-serif;
        font-size: @vw16;
        letter-spacing: .08em;
        color: @primaryColor;
        text-transform: none;
        line-height: 1.2;

        // Circle before text
        &::before {
        content: '';
        display: inline-block;
        width: @vw16;
        height: @vw16;
        border-radius: 50%;
        border: 2px solid fade(@primaryColor, 80%);
        background: transparent;
        margin-right: @vw12;
        transition: all .25s ease;
        }
    }

    // Hover state for circle
    .wpcf7-acceptance .wpcf7-list-item > label:hover
        .wpcf7-list-item-label::before {
        border-color: @primaryColor;
    }

    // Checked state fill
    .wpcf7-acceptance input[type="checkbox"]:checked
        + .wpcf7-list-item-label::before {
        background: @primaryColor;
        border-color: @primaryColor;
    }

    // Optional: inner dot for contrast
    .wpcf7-acceptance input[type="checkbox"]:checked
        + .wpcf7-list-item-label::after {
        content: '';
        display: inline-block;
        width: @vw8;
        height: @vw8;
        border-radius: 50%;
        background: @backgroundColor;
        margin-left: -(@vw16 + @vw12);
        transform: translateX(@vw11);
    }

}

@media all and (max-width: 1080px) {
    .wpcf7-form {
        width: 80%;

        input.wpcf7-form-control,
        textarea.wpcf7-form-control,
        select.wpcf7-form-control {
            .rounded(@vw60-1080);
            font-size: @vw16-1080;
        }

        // Select2 responsive styling for 1080px
        .select2-container {
            .select2-selection--single {
                .rounded(@vw60-1080);
                font-size: @vw16-1080;
            }
        }

        .select2-dropdown {
            .rounded(@vw10-1080);

            .select2-results__options {
                .select2-results__option {
                    font-size: @vw16-1080;
                }
            }
        }

        .wpcf7-form-control-wrap {
            &:not(:last-child) {
                margin-bottom: @vw10-1080;
            }
        }

        .wpcf7-not-valid-tip, .wpcf7-response-output {
            margin-top: @vw5-1080;
            .rounded(@vw10-1080);
            padding: @vw10-1080 @vw16-1080 !important;
            font-size: @vw16-1080;
        }

        textarea {
            min-height: @vw100-1080;
            height: @vw100-1080 * 2;
        }

        .buttonWrapper {
            margin-top: @vw40-1080;
        }

        .wpcf7-form-control-wrap[data-name^="acceptance"] {
            margin-top: @vw20-1080;
        }

        .wpcf7-acceptance .wpcf7-list-item > label {
            gap: @vw12-1080;
        }

        .wpcf7-acceptance .wpcf7-list-item-label {
            font-size: @vw16-1080;

            &::before {
                width: @vw16-1080;
                height: @vw16-1080;
                margin-right: @vw12-1080;
            }
        }

        .wpcf7-acceptance input[type="checkbox"]:checked
            + .wpcf7-list-item-label::after {
            width: @vw8-1080;
            height: @vw8-1080;
            margin-left: -(@vw16-1080 + @vw12-1080);
            transform: translateX(@vw11-1080);
        }
    }
}

@media all and (max-width: 580px) {
    .wpcf7-form {
        width: 100%;

        input.wpcf7-form-control,
        textarea.wpcf7-form-control,
        select.wpcf7-form-control {
            .rounded(@vw60-580);
            font-size: @vw16-580;
        }

        // Select2 responsive styling for 580px
        .select2-container {
            .select2-selection--single {
                .rounded(@vw60-580);
                font-size: @vw16-580;
            }
        }

        .select2-dropdown {
            .rounded(@vw10-580);

            .select2-results__options {
                .select2-results__option {
                    font-size: @vw16-580;
                }
            }
        }

        .wpcf7-form-control-wrap {
            width: 100%;
            &:not(:last-child) {
                margin-bottom: @vw16-580;
            }
        }

        .wpcf7-not-valid-tip, .wpcf7-response-output {
            margin-top: @vw5-580;
            .rounded(@vw10-580);
            padding: @vw10-580 @vw16-580 !important;
            font-size: @vw16-580;
        }

        textarea {
            min-height: @vw100-580;
            height: @vw100-580 * 2;
        }

        .buttonWrapper {
            margin-top: @vw40-580;
        }

        .wpcf7-form-control-wrap[data-name^="acceptance"] {
            margin-top: @vw20-580;
        }

        .wpcf7-acceptance .wpcf7-list-item > label {
            gap: @vw12-580;
        }

        .wpcf7-acceptance .wpcf7-list-item-label {
            font-size: @vw16-580;

            &::before {
                width: @vw16-580;
                height: @vw16-580;
                margin-right: @vw12-580;
            }
        }

        .wpcf7-acceptance input[type="checkbox"]:checked
            + .wpcf7-list-item-label::after {
            width: @vw8-580;
            height: @vw8-580;
            margin-left: -(@vw16-580 + @vw12-580);
            transform: translateX(@vw11-580);
        }
    }
}