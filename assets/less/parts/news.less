// out: false
@import '../vw_values.less';
@import '../constants.less';

.newsArticle {
    &.inview {
        .articleBody {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            -webkit-transition: opacity .45s, transform .45s;
        }
    }
    .articleContent {
    margin: 0 auto;
    }

    .textTitle, .bigTitle {
    text-align: center;
    margin: @vw50 0;
    }

    .socialSharing {
        display: flex;
        justify-content: center;
        gap: @vw16;
        margin-bottom: @vw50;
        padding-bottom: @vw30;
        border-bottom: 1px solid #eee;
    }

    .socialShareLink {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: @vw40;
    height: @vw40;
    .rounded(50%);
    background: @primaryColor;
    color: white;
    text-decoration: none;
    .transition(.3s);
    * {
        cursor: pointer;
    }
    }

    .socialShareLink:hover {
    background: #a08660;
    transform: translateY(-2px);
    }

    .articleBody {
        opacity: 0;
        .transform(translateY(@vw16));
        padding: 0 @vw100 * 2;
    }

    .articleBody img {
    max-width: 100%;
    height: auto;
    margin: @vw30 0;
    .rounded(@vw12);
    overflow: hidden;
    object-fit: cover;
    width: 100%;
    position: relative;
    }
    ol, ul {
        line-height: 1.5;
    }
}

.newsOverviewBlock {
    &.inview {
        .newsItem {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity .45s, transform .45s;
            -webkit-transition: opacity .45s, transform .45s;
            .stagger(100, 0.15s, 0.15s);
        }
    }
    .highlights {
        display: flex;
        gap: @vw32;
        margin-bottom: @vw100;
    }
    .innerCol {
        width: 33.3333%;
        height: 100%;
        &:first-child {
            width: 66.6666%;
            .newsItem {
                .newsImage {
                    .paddingRatio(.83, .6);
                }
            }
        }
        &:last-child {
            display: inline-block;
            flex-direction: column;
            justify-content: space-between;
            .newsItem {
                &:not(:last-child) {
                    margin-bottom: @vw22;
                }
            }
        }
    }
    .newsItem {
        color: @secondaryColor;
        display: block;
        text-decoration: none;
        opacity: 0;
        .transform(translateY(@vw16));
        cursor: pointer;
        * {
            cursor: pointer;
        }
        &:hover {
            .newsContent {
                opacity: .4;
            }
        }
    }
    .newsImage {
        overflow: hidden;
        position: relative;
        .paddingRatio(1, .6);
        .rounded(@vw12);
        img {
            position: absolute;
            top: 50%;
            left: 50%;
            .transform(translate(-50%, -50%));
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
    }
    .newsContent {
        padding-top: @vw12;
        .transitionMore(opacity, .3s);
    }
    .newsMeta {
        margin-bottom: @vw10;
    }
    .allNews {
        .newsGrid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: @vw16;
        }
    }
}

@media all and (max-width: 1080px) {
    .newsArticle {
        .textTitle, .bigTitle {
            margin: @vw50-1080 0;
        }

        .socialSharing {
            gap: @vw20-1080;
            margin-bottom: @vw50-1080;
            padding-bottom: @vw30-1080;
        }

        .socialShareLink {
            width: @vw40-1080;
            height: @vw40-1080;
        }

        .articleBody {
            padding: 0 @vw80-1080;
            .transform(translateY(@vw16-1080));
        }

        .articleBody img {
            margin: @vw30-1080 0;
            .rounded(@vw12-1080);
        }
    }
    .newsOverviewBlock {
        .highlights {
            gap: @vw32-1080;
            margin-bottom: @vw100-1080;
        }
        .innerCol {
            width: 33.3333%;
            height: 100%;
            &:first-child {
                width: 66.6666%;
                .newsItem {
                    .newsImage {
                        .paddingRatio(.83, .6);
                    }
                }
            }
            &:last-child {
                .newsItem {
                    &:not(:last-child) {
                        margin-bottom: @vw22-1080;
                    }
                }
            }
        }
        .newsItem {
            .transform(translateY(@vw16-1080));
        }
        .newsImage {
            .paddingRatio(1, .6);
            .rounded(@vw12-1080);
        }
        .newsContent {
            padding-top: @vw12-1080;
        }
        .newsMeta {
            margin-bottom: @vw10-1080;
        }
        .allNews {
            .newsGrid {
                grid-template-columns: 1fr 1fr 1fr;
                gap: @vw16-1080;
            }
        }
    }
}

@media all and (max-width: 580px) {
    .newsArticle {
        .textTitle, .bigTitle {
            margin: @vw50-580 0;
        }

        .socialSharing {
            gap: @vw20-580;
            margin-bottom: @vw50-580;
            padding-bottom: @vw30-580;
        }

        .socialShareLink {
            width: @vw40-580;
            height: @vw40-580;
        }

        .articleBody {
            padding: 0;
            .transform(translateY(@vw16-580));
        }

        .articleBody img {
            margin: @vw30-580 0;
            .rounded(@vw12-580);
        }
    }
    .newsOverviewBlock {
        .highlights {
            gap: @vw32-580;
            margin-bottom: @vw40-580;
            flex-direction: column;
        }
        .innerCol {
            width: 100%;
            height: auto;
            &:first-child {
                width: 100%;
                .newsItem {
                    .newsImage {
                        .paddingRatio(1, .6);
                    }
                }
            }
            &:last-child {
                width: 100%;
                .newsItem {
                    &:not(:last-child) {
                        margin-bottom: @vw40-580;
                    }
                }
            }
        }
        .newsItem {
            .transform(translateY(@vw16-580));
        }
        .newsImage {
            .paddingRatio(1, .6);
            .rounded(@vw12-580);
        }
        .newsContent {
            padding-top: @vw12-580;
        }
        .newsMeta {
            margin-bottom: @vw10-580;
        }
        .allNews {
            .newsGrid {
                display: block;
                gap: 0;
                .newsItem {
                    width: 100%;
                    &:not(:first-child) {
                        margin-top: @vw40-580;
                    }
                    .newsImage {
                        .paddingRatio(1, .6);
                    }
                }
            }
        }
    }
}